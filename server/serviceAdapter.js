import express from 'express';
import {
  CopilotRuntime,
  ExperimentalEmptyAdapter,
  copilotRuntimeNodeExpressEndpoint,
} from '@copilotkit/runtime';
import { AgnoAgent } from '@ag-ui/agno';

const app = express();

// 使用空适配器，因为我们只使用一个代理
const serviceAdapter = new ExperimentalEmptyAdapter();

// 获取基础URL，用于构建后端请求
const getBaseUrl = () => {
  return process.env.AGNO_SERVICE_URL || "https://agent-demo.8btc-ops.com";
};

// 配置动态agent路由处理CopilotKit请求
app.use('/agui', (req, res, next) => {
  (async () => {
    const agentId = req.headers.agentId || 'web_agent';

    console.log(`📥 收到请求: ${req.method} ${req.url}, agentId: ${agentId}, `);

    // 检查Authorization header
    const authHeader = req.headers.authorization;

    // 构建后端AgnoAgent的URL，格式为 */aguiapi/:agentId/run
    const agentUrl = `${getBaseUrl()}/aguiapi/${agentId}/run`;

    console.log(`🔗 处理Agent请求: ${agentId}, 后端URL: ${agentUrl}`);

    // 为当前请求动态创建AgnoAgent实例
    const agnoAgent = new AgnoAgent({
      url: agentUrl,
      headers: {
        Authorization: authHeader,
      },
    });

    // 创建CopilotRuntime实例，配置当前的Agno代理
    const runtime = new CopilotRuntime({
      agents: {
        "agno_agent": agnoAgent,
      },
    });

    const handler = copilotRuntimeNodeExpressEndpoint({
      endpoint: `/agui`,
      runtime,
      serviceAdapter,
    });

    return handler(req, res);
  })().catch((error) => {
    console.error(`❌ 处理请求时出错:`, error);
    next(error);
  });
});

// 健康检查端点
app.get('/health', (_req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

const PORT = process.env.SERVICE_ADAPTER_PORT || 4000;

app.listen(PORT, () => {
  console.log(`🚀 ServiceAdapter服务已启动，监听端口: ${PORT}`);
  console.log(`📡 CopilotKit端点: http://localhost:${PORT}/agui`);
  console.log(`🔗 Agno服务地址: ${process.env.AGNO_SERVICE_URL || "https://agent-demo.8btc-ops.com/v2/agui"}`);
});
