import { ConfigProvider } from 'antd';
import { RouterProvider } from 'react-router-dom';
import zhCN from 'antd/locale/zh_CN';
import router from './router';
import StoreProvider from './store/providers';
import { themeConfig } from './constants/theme';
import { CopilotKit } from '@copilotkit/react-core';
import { useAtomValue } from 'jotai';
import {
  currentAgentAtom,
  tokenAtom,
  selectedSessionIdAtom,
} from './store/atoms';

import '@copilotkit/react-ui/styles.css';

// App 组件：应用的根组件，设置全局配置和路由
const AppContent: GenieType.FC = () => {
  const currentAgent = useAtomValue(currentAgentAtom);
  const token = useAtomValue(tokenAtom);
  const selectedSessionId = useAtomValue(selectedSessionIdAtom);

  console.log('currentAgent', currentAgent?.id);
  console.log('selectedSessionId', selectedSessionId);

  return (
    <>
      <CopilotKit
        runtimeUrl={'/agui'}
        agent="agno_agent"
        threadId={selectedSessionId}
        headers={{
          Authorization: token,
          agentId: currentAgent?.id || 'web_agent',
        }}
        properties={{ agentId: currentAgent?.id || 'web_agent' }}
        showDevConsole={true}
        publicLicenseKey="ck_pub_48592d2b811d8a6383c3c868bcb84d64"
        onError={error => {
          console.log('👿 copkit err:', error);
        }}
      >
        <ConfigProvider locale={zhCN} theme={themeConfig}>
          <RouterProvider router={router} />
        </ConfigProvider>
      </CopilotKit>
    </>
  );
};

const App: GenieType.FC = () => {
  return (
    <StoreProvider>
      <AppContent />
    </StoreProvider>
  );
};

export default App;
