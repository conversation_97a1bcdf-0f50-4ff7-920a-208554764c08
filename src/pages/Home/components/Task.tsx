import { memo, useEffect } from 'react';
import { CopilotChat } from '@copilotkit/react-ui';
import { themeCopilotKitConfig } from '@/constants/theme';
import classnames from 'classnames';
import {
  useCopilotContext,
  useCopilotChat,
  useCopilotMessagesContext,
} from '@copilotkit/react-core';
import { useAtomValue } from 'jotai';
import { selectedSessionIdAtom } from '@/store/atoms';
import AgentList from './AgentList';
import { SendOutlined } from '@ant-design/icons';
import { agentServices } from '@/services/agent';

type TaskProps = Record<string, never>;

const Task: GenieType.FC<TaskProps> = memo(() => {
  const context = useCopilotContext();
  console.log('useCopilotContext', context);
  const chat = useCopilotChat();
  console.log('useCopilotChat', chat);
  const messages = useCopilotMessagesContext();
  console.log('useCopilotMessagesContext', messages);
  const selectedSessionId = useAtomValue(selectedSessionIdAtom);
  const isTaskStart = messages.messages.length > 0;

  const getSessionDetail = async (selectedSessionId: string) => {
    const res = await agentServices.session(selectedSessionId);
    console.log('handleAgentChange', res);
    // 填充历史消息
    messages.setMessages([
      {
        content: 'Hello World',
        role: 'user',
        created_at: Date.now(),
        id: '1',
      },
    ]);
  };

  // 当selectedSessionId变化时，处理消息加载
  useEffect(() => {
    console.log('selectedSessionId变化:', selectedSessionId);
    if (selectedSessionId === undefined) {
      // 新聊天：清除当前消息
      console.log('开始新聊天，清除消息');
      // 注意：CopilotKit可能会自动处理这个，或者我们需要调用特定的API
      messages.setMessages([]);
    } else {
      // 加载历史聊天：这里CopilotKit应该自动加载threadId对应的消息
      console.log('加载历史聊天:', selectedSessionId);
      getSessionDetail(selectedSessionId);
    }
  }, [selectedSessionId]);

  return (
    <div
      style={themeCopilotKitConfig}
      className="h-full w-full max-w-[80%] mx-auto"
    >
      {!isTaskStart && (
        <div className="w-full text-white text-center leading-[130%] font-bold text-[38px] pt-[28vh]">
          Build the bridge from Web3 to AGI
          <br />
          Make Crypto Wisdom Eternal
        </div>
      )}
      <CopilotChat
        className={classnames('h-auto w-full', {
          'h-full': isTaskStart,
        })}
        imageUploadsEnabled
        inputFileAccept="image/png, image/jpeg, image/jpg, application/pdf"
        labels={{
          placeholder: 'How can I help you today?',
        }}
        icons={{
          sendIcon: <SendOutlined />,
        }}
      />
      {!isTaskStart && <AgentList />}
    </div>
  );
});

Task.displayName = 'Task';

export default Task;
