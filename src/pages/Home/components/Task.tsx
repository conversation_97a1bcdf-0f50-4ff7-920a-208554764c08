import { memo, useEffect } from 'react';
import { CopilotChat } from '@copilotkit/react-ui';
import { themeCopilotKitConfig } from '@/constants/theme';
import classnames from 'classnames';
import {
  useCopilotContext,
  useCopilotChat,
  useCopilotMessagesContext,
} from '@copilotkit/react-core';
import { useAtomValue } from 'jotai';
import { selectedSessionIdAtom } from '@/store/atoms';
import AgentList from './AgentList';
import { SendOutlined } from '@ant-design/icons';
import { agentServices } from '@/services/agent';

type TaskProps = Record<string, never>;

const Task: GenieType.FC<TaskProps> = memo(() => {
  const context = useCopilotContext();
  console.log('useCopilotContext', context);
  const chat = useCopilotChat();
  console.log('useCopilotChat', chat);
  const messages = useCopilotMessagesContext();
  console.log('useCopilotMessagesContext', messages);
  const selectedSessionId = useAtomValue(selectedSessionIdAtom);
  const isTaskStart = messages.messages.length > 0;

  // 消息格式转换函数
  const transformChatHistory = (chatHistory: any[]) => {
    if (!chatHistory || !Array.isArray(chatHistory)) {
      console.log('聊天历史数据无效或为空');
      return [];
    }

    return chatHistory.map((message: any, index: number) => {
      // 基础消息格式
      const baseMessage = {
        id: `msg-${message.created_at}-${index}`, // 生成唯一ID
        role: message.role, // system, user, assistant
        content: message.content || '', // 消息内容
        createdAt: new Date(message.created_at * 1000), // 转换Unix时间戳为Date对象
      };

      // 如果是assistant消息且有metrics，添加额外信息
      if (message.role === 'assistant' && message.metrics) {
        return {
          ...baseMessage,
          metadata: {
            metrics: message.metrics,
            from_history: message.from_history,
            stop_after_tool_call: message.stop_after_tool_call,
          },
        };
      }

      // 为其他类型消息添加基础metadata
      return {
        ...baseMessage,
        metadata: {
          from_history: message.from_history,
          stop_after_tool_call: message.stop_after_tool_call,
        },
      };
    });
  };

  const getSessionDetail = async (selectedSessionId: string) => {
    try {
      const res = await agentServices.session(selectedSessionId);
      console.log('获取会话详情:', res);

      // 从API响应中提取数据
      const sessionData = (res.data || res) as any;
      console.log('会话数据:', sessionData);

      // 检查是否有聊天历史
      if (
        !sessionData?.chat_history ||
        !Array.isArray(sessionData.chat_history)
      ) {
        console.log('没有聊天历史数据');
        return;
      }

      console.log('原始聊天历史数据:', sessionData.chat_history);
      console.log('聊天历史消息数量:', sessionData.chat_history.length);

      // 尝试使用appendMessage逐个添加历史消息
      // 但是先清空现有消息
      messages.setMessages([]);

      // 等待一下让清空操作完成
      await new Promise(resolve => setTimeout(resolve, 100));

      // 逐个添加历史消息，跳过system消息
      for (const message of sessionData.chat_history) {
        if (message.role === 'system') {
          console.log('跳过system消息');
          continue;
        }

        console.log(
          '添加消息:',
          message.role,
          message.content?.substring(0, 50) + '...'
        );

        try {
          // 使用chat.appendMessage方法
          await chat.appendMessage({
            role: message.role,
            content: message.content,
            id: `history-${message.created_at}`,
          });
        } catch (error) {
          console.error('添加消息失败:', error);
        }
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error);
    }
  };

  // 当selectedSessionId变化时，处理消息加载
  useEffect(() => {
    console.log('selectedSessionId变化:', selectedSessionId);
    if (selectedSessionId === undefined) {
      // 新聊天：清除当前消息
      console.log('开始新聊天，清除消息');
      // 注意：CopilotKit可能会自动处理这个，或者我们需要调用特定的API
      messages.setMessages([]);
    } else {
      // 加载历史聊天：这里CopilotKit应该自动加载threadId对应的消息
      console.log('加载历史聊天:', selectedSessionId);
      getSessionDetail(selectedSessionId);
    }
  }, [selectedSessionId]);

  return (
    <div
      style={themeCopilotKitConfig}
      className="h-full w-full max-w-[80%] mx-auto"
    >
      {!isTaskStart && (
        <div className="w-full text-white text-center leading-[130%] font-bold text-[38px] pt-[28vh]">
          Build the bridge from Web3 to AGI
          <br />
          Make Crypto Wisdom Eternal
        </div>
      )}
      <CopilotChat
        className={classnames('h-auto w-full', {
          'h-full': isTaskStart,
        })}
        imageUploadsEnabled
        inputFileAccept="image/png, image/jpeg, image/jpg, application/pdf"
        labels={{
          placeholder: 'How can I help you today?',
        }}
        icons={{
          sendIcon: <SendOutlined />,
        }}
      />
      {!isTaskStart && <AgentList />}
    </div>
  );
});

Task.displayName = 'Task';

export default Task;
