import { memo, useEffect } from 'react';
import { CopilotChat } from '@copilotkit/react-ui';
import { themeCopilotKitConfig } from '@/constants/theme';
import classnames from 'classnames';
import {
  useCopilotContext,
  useCopilotChat,
  useCopilotMessagesContext,
} from '@copilotkit/react-core';
import { useAtomValue } from 'jotai';
import { selectedSessionIdAtom } from '@/store/atoms';
import AgentList from './AgentList';
import { SendOutlined } from '@ant-design/icons';
import { agentServices } from '@/services/agent';

type TaskProps = Record<string, never>;

const Task: GenieType.FC<TaskProps> = memo(() => {
  const context = useCopilotContext();
  console.log('useCopilotContext', context);
  const chat = useCopilotChat();
  console.log('useCopilotChat', chat);
  const messages = useCopilotMessagesContext();
  console.log('useCopilotMessagesContext', messages);
  const selectedSessionId = useAtomValue(selectedSessionIdAtom);
  const isTaskStart = messages.messages.length > 0;

  const getSessionDetail = async (selectedSessionId: string) => {
    const res = await agentServices.session(selectedSessionId);
    console.log('handleAgentChange', res);
    messages.setMessages(res?.chat_history);
  };[
    {
        "content": "You are an AI artist and assistant. You can understand prompts, generate\nimages via the NanoBanana tool, and provide a coherent Markdown-formatted\nresponse that includes the generated images.\n\n<instructions>\n1. Analyze the user's request carefully.\n2. If image generation is needed, use the `nano_tool` tool.\n3. Convert tool results into Markdown format, e.g., `![desc](image_url)`.\n4. Return a coherent, user-friendly response with text and images.\n\n</instructions>\n\n<additional_information>\n- Use markdown to format your answers.\n- The current time is 2025-09-28 03:10:11.855575.\n</additional_information>\n\nYou have access to memories from previous interactions with the user that you can use:\n\n<memories_from_previous_interactions>\n- 用户喜欢小猫咪和蓝色的帽子。\n- 用户喜欢小猫咪戴着紫色和蓝色的帽子。\n- 用户希望生成一张小猫的图片。\n- 用户希望生成一只小鸟的图片。\n- 用户希望生成一张杭州的图表。\n- 用户希望生成一张杭州的图表。\n</memories_from_previous_interactions>\n\nNote: this information is from previous interactions and may be updated in this conversation. You should always prefer information from this conversation over the past memories.\n\n<updating_user_memories>\n- You have access to the `update_user_memory` tool that you can use to add new memories, update existing memories, delete memories, or clear all memories.\n- If the user's message includes information that should be captured as a memory, use the `update_user_memory` tool to update your memory database.\n- Memories should include details that could personalize ongoing interactions with the user.\n- Use this tool to add new memories or update existing memories that you identify in the conversation.\n- Use this tool if the user asks to update their memory, delete a memory, or clear all memories.\n- If you use the `update_user_memory` tool, remember to pass on the response to the user.\n</updating_user_memories>",
        "from_history": false,
        "stop_after_tool_call": false,
        "role": "system",
        "created_at": 1759029011
    },
    {
        "content": "hey",
        "from_history": false,
        "stop_after_tool_call": false,
        "role": "user",
        "created_at": 1759029011
    },
    {
        "content": "Hey! 👋 \n\nI'm here to help you with anything you need! Whether you want me to generate some images, search for information, help with files, or just chat - I'm ready.\n\nFrom our previous interactions, I remember you like cats with blue and purple hats, and you've asked about birds and Hangzhou charts before. What would you like to do today?",
        "from_history": false,
        "stop_after_tool_call": false,
        "role": "assistant",
        "metrics": {
            "input_tokens": 1609,
            "output_tokens": 77,
            "total_tokens": 1686
        },
        "created_at": 1759029011
    }
]

  // 当selectedSessionId变化时，处理消息加载
  useEffect(() => {
    console.log('selectedSessionId变化:', selectedSessionId);
    if (selectedSessionId === undefined) {
      // 新聊天：清除当前消息
      console.log('开始新聊天，清除消息');
      // 注意：CopilotKit可能会自动处理这个，或者我们需要调用特定的API
      messages.setMessages([]);
    } else {
      // 加载历史聊天：这里CopilotKit应该自动加载threadId对应的消息
      console.log('加载历史聊天:', selectedSessionId);
      getSessionDetail(selectedSessionId);
    }
  }, [selectedSessionId]);

  return (
    <div
      style={themeCopilotKitConfig}
      className="h-full w-full max-w-[80%] mx-auto"
    >
      {!isTaskStart && (
        <div className="w-full text-white text-center leading-[130%] font-bold text-[38px] pt-[28vh]">
          Build the bridge from Web3 to AGI
          <br />
          Make Crypto Wisdom Eternal
        </div>
      )}
      <CopilotChat
        className={classnames('h-auto w-full', {
          'h-full': isTaskStart,
        })}
        imageUploadsEnabled
        inputFileAccept="image/png, image/jpeg, image/jpg, application/pdf"
        labels={{
          placeholder: 'How can I help you today?',
        }}
        icons={{
          sendIcon: <SendOutlined />,
        }}
      />
      {!isTaskStart && <AgentList />}
    </div>
  );
});

Task.displayName = 'Task';

export default Task;
