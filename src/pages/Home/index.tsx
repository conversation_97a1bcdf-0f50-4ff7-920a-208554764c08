import { useState, memo } from 'react';
import { useAtom } from 'jotai';
import { selectedSessionIdAtom } from '@/store/atoms';
import ChatHistory from './components/ChatHistory';

import img_bg from '@/assets/images/login/bg.png';
import Task from './components/Task';

type HomeProps = Record<string, never>;

const Home: GenieType.FC<HomeProps> = memo(() => {
  const [collapsed, setCollapsed] = useState(false);
  const [selectedSessionId, setSelectedSessionId] = useAtom(
    selectedSessionIdAtom
  );

  // 处理聊天历史选择
  const handleSessionSelect = (sessionId: string | undefined) => {
    console.log('选择会话:', sessionId);
    setSelectedSessionId(sessionId);
  };

  return (
    <div
      className="w-full h-full flex"
      style={{
        background: `#000 url(${img_bg}) center/cover`,
      }}
    >
      <ChatHistory
        collapsed={collapsed}
        onToggle={() => setCollapsed(!collapsed)}
        onSessionSelect={handleSessionSelect}
      />
      <div
        className="flex-1 py-[15px] pr-[15px] h-full"
        style={{ paddingLeft: collapsed ? '15px' : '0' }}
      >
        <div className="w-full h-full rounded-[10px] bg-black/80">
          <Task />
        </div>
      </div>
    </div>
  );
});

Home.displayName = 'Home';

export default Home;
