import { atom } from 'jotai';
import { atomWithStorage } from 'jotai/utils';
import { AgentDetails } from '@/types/agno';

// ==================== 基础状态原子 ====================

/**
 * 当前选择的 Agent
 */
export const currentAgentAtom = atom<AgentDetails | undefined>(undefined);

/**
 * 当前活跃任务
 */
export const activeTaskAtom = atom<CHAT.Task | undefined>(undefined);

/**
 * 是否显示操作面板
 */
export const showActionPanelAtom = atom<boolean>(false);

/**
 * 当前选中的会话ID
 */
export const selectedSessionIdAtom = atom<string | undefined>(undefined);

// ==================== 持久化状态 ====================

/**
 * 主题设置 (自动保存到 localStorage)
 */
export const themeAtom = atomWithStorage<'light' | 'dark'>('theme', 'light');

/**
 * 语言设置 (自动保存到 localStorage)
 */
export const languageAtom = atomWithStorage<'zh-CN' | 'en-US'>(
  'language',
  'zh-CN'
);

/**
 * 用户偏好设置 (自动保存到 localStorage)
 */
export const userPreferencesAtom = atomWithStorage('userPreferences', {
  autoScroll: true,
  showTimestamp: true,
  soundEnabled: false,
});

// token
const stringStorage = {
  getItem: (key: string) => localStorage.getItem(key) ?? '',
  setItem: (key: string, value: string) => localStorage.setItem(key, value),
  removeItem: (key: string) => localStorage.removeItem(key),
};

export const tokenAtom = atomWithStorage('token', '', stringStorage);

// ==================== 派生状态（只读计算属性） ====================

/**
 * 输入是否有效
 */
// export const isInputValidAtom = atom(get => {
//   const inputInfo = get(inputInfoAtom);
//   return inputInfo.message.trim().length > 0;
// });
